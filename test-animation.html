<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Animation Test</title>
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 50px;
            text-align: center;
        }
        
        .hero-main-title {
            font-size: 3.5rem;
            font-weight: 700;
            line-height: 1.2;
            margin-bottom: 1.5rem;
            color: #2c3e50;
        }
        
        .text-gradient {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        /* Animated Text Slider */
        .animated-text {
            position: relative;
            display: inline-block;
            height: 1.2em;
            overflow: hidden;
            vertical-align: baseline;
        }

        .text-item {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            opacity: 0;
            transform: translateY(100%);
            transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            white-space: nowrap;
        }

        .text-item.active {
            opacity: 1;
            transform: translateY(0);
        }

        .hero-actions {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
            justify-content: center;
            align-items: center;
            margin-top: 2rem;
        }

        .btn-primary {
            background: #28a745;
            color: white;
            padding: 14px 32px;
            border-radius: 50px;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
            display: inline-block;
            margin: 0 8px;
            border: none;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        }

        .btn-outline {
            background: transparent;
            color: #28a745;
            border: 2px solid #28a745;
            padding: 12px 30px;
            border-radius: 50px;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            margin: 0 8px;
        }
    </style>
</head>
<body>
    <h1 class="hero-main-title">
        Powering the Future with<br>
        <span class="text-gradient animated-text">
            <span class="text-item active">Offshore Wind</span>
            <span class="text-item">Solar Innovation</span>
            <span class="text-item">Renewable Energy</span>
        </span>
    </h1>
    
    <div class="hero-actions text-center">
        <a href="#" class="btn-primary">Explore Our Projects</a>
        <a href="#" class="btn-outline">Learn More</a>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Animated Text Slider for Hero Title
            const animatedText = document.querySelector('.animated-text');
            if (animatedText) {
                const textItems = animatedText.querySelectorAll('.text-item');
                let currentTextIndex = 0;
                
                function showNextText() {
                    // Remove active class from all text items
                    textItems.forEach(item => item.classList.remove('active'));
                    
                    // Move to next text
                    currentTextIndex = (currentTextIndex + 1) % textItems.length;
                    
                    // Add active class to new text
                    textItems[currentTextIndex].classList.add('active');
                }
                
                // Initialize first text as active
                if (textItems.length > 0) {
                    textItems[0].classList.add('active');
                }
                
                // Start the text animation
                setInterval(showNextText, 3000); // Change text every 3 seconds
                
                console.log('Animated text slider initialized successfully!');
                console.log('Found', textItems.length, 'text items');
            }
        });
    </script>
</body>
</html>
