<?php include 'header.php'; ?>

    <!-- Modern Hero Section -->
    <section id="modern-hero" class="modern-hero-section">
        <div class="hero-background-wrapper">
            <div class="hero-background-image">
                <img src="https://images.unsplash.com/photo-1497435334941-8c899ee9e8e9?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80" alt="Renewable Energy Future" class="hero-bg-img">
            </div>
            <div class="hero-gradient-overlay"></div>
            <div class="hero-particles"></div>
        </div>
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-10 text-center">
                    <div class="hero-content-modern">
                        <div class="hero-badge-modern">
                            <i class="fas fa-leaf"></i>
                            <span>About Eldergrove Energy</span>
                        </div>
                        <h1 class="hero-title-modern">
                            Pioneering the <span class="text-gradient-modern">Future</span><br>
                            of Renewable Energy
                        </h1>
                        <p class="hero-description-modern">
                            Leading the global transition to sustainable energy with innovative solutions
                            that power communities, protect our planet, and create lasting positive impact.
                        </p>
                        <div class="hero-actions-modern">
                            <a href="#company-intro" class="btn-modern-primary">
                                <span>Discover Our Story</span>
                                <i class="fas fa-arrow-right"></i>
                            </a>
                            <a href="#our-services" class="btn-modern-outline">
                                <span>Our Solutions</span>
                            </a>
                        </div>
                        <div class="hero-scroll-indicator">
                            <div class="scroll-arrow">
                                <i class="fas fa-chevron-down"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Company Introduction Section -->
    <section id="company-intro" class="company-intro-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <div class="intro-content">
                        <div class="section-badge-modern">
                            <i class="fas fa-building"></i>
                            <span>Who We Are</span>
                        </div>
                        <h2 class="section-title-modern">
                            Transforming Energy,<br>
                            <span class="text-accent">Empowering Tomorrow</span>
                        </h2>
                        <p class="intro-description">
                            Founded in 1971, Eldergrove Energy has evolved from a visionary startup to a global leader
                            in renewable energy solutions. We specialize in wind, solar, biomass, and energy storage
                            technologies that are reshaping how the world powers itself.
                        </p>
                        <div class="intro-actions">
                            <a href="contact-page.php" class="btn-about-primary">
                                <span>Get Started</span>
                                <i class="fas fa-arrow-right"></i>
                            </a>
                        </div>

                        <style>
                        .intro-actions {
                            margin-top: 2rem;
                        }

                        .btn-about-primary {
                            display: inline-flex;
                            align-items: center;
                            gap: 0.75rem;
                            background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
                            color: white;
                            padding: 1rem 2rem;
                            border-radius: 8px;
                            text-decoration: none;
                            font-weight: 600;
                            font-size: 1rem;
                            transition: all 0.3s ease;
                            box-shadow: 0 4px 15px rgba(34, 197, 94, 0.3);
                        }

                        .btn-about-primary:hover {
                            transform: translateY(-2px);
                            box-shadow: 0 8px 25px rgba(34, 197, 94, 0.4);
                            color: white;
                            text-decoration: none;
                        }
                        </style>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="intro-visual">
                        <div class="visual-card">
                            <img src="https://images.unsplash.com/photo-1559302504-64aae6ca6b6d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="Eldergrove Team" class="intro-image">
                            <div class="visual-overlay">
                                <div class="play-button">
                                    <i class="fas fa-play"></i>
                                </div>
                            </div>
                        </div>
                        <div class="floating-stats">
                            <div class="floating-stat">
                                <span class="stat-number">25GW+</span>
                                <span class="stat-label">Clean Energy</span>
                            </div>
                            <div class="floating-stat">
                                <span class="stat-number">20+</span>
                                <span class="stat-label">Years</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Our Services Section -->
    <section id="our-services" class="services-section">
        <div class="container">
            <div class="section-header-modern text-center">
                <div class="section-badge-modern">
                    <i class="fas fa-cogs"></i>
                    <span>Our Solutions</span>
                </div>
                <h2 class="section-title-modern">
                    Comprehensive <span class="text-accent">Energy Solutions</span>
                </h2>
                <p class="section-description-modern">
                    From concept to completion, we deliver end-to-end renewable energy solutions
                    that drive sustainable growth and environmental stewardship.
                </p>
            </div>

            <div class="row">
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="service-card">
                        <div class="service-icon">
                            <i class="fas fa-wind"></i>
                        </div>
                        <h3 class="service-title">Wind Energy</h3>
                        <p class="service-description">
                            Advanced onshore and offshore wind solutions with cutting-edge turbine technology
                            and smart grid integration.
                        </p>
                        <div class="service-features">
                            <span class="feature-tag">Onshore Wind</span>
                            <span class="feature-tag">Offshore Wind</span>
                            <span class="feature-tag">Floating Wind</span>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="service-card">
                        <div class="service-icon">
                            <i class="fas fa-solar-panel"></i>
                        </div>
                        <h3 class="service-title">Solar Power</h3>
                        <p class="service-description">
                            Utility-scale solar farms and distributed solar systems with advanced
                            photovoltaic technology and energy optimization.
                        </p>
                        <div class="service-features">
                            <span class="feature-tag">Solar Farms</span>
                            <span class="feature-tag">Rooftop Solar</span>
                            <span class="feature-tag">Solar Tracking</span>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="service-card">
                        <div class="service-icon">
                            <i class="fas fa-battery-full"></i>
                        </div>
                        <h3 class="service-title">Energy Storage</h3>
                        <p class="service-description">
                            Next-generation battery storage systems and grid stabilization solutions
                            for reliable renewable energy delivery.
                        </p>
                        <div class="service-features">
                            <span class="feature-tag">Grid Storage</span>
                            <span class="feature-tag">Microgrids</span>
                            <span class="feature-tag">Smart Systems</span>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="service-card">
                        <div class="service-icon">
                            <i class="fas fa-seedling"></i>
                        </div>
                        <h3 class="service-title">Biomass Energy</h3>
                        <p class="service-description">
                            Sustainable biomass power generation using agricultural waste and
                            dedicated energy crops for carbon-neutral electricity.
                        </p>
                        <div class="service-features">
                            <span class="feature-tag">Waste-to-Energy</span>
                            <span class="feature-tag">Biogas</span>
                            <span class="feature-tag">Carbon Neutral</span>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="service-card">
                        <div class="service-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <h3 class="service-title">Energy Consulting</h3>
                        <p class="service-description">
                            Strategic energy planning, feasibility studies, and sustainability
                            consulting for businesses and governments.
                        </p>
                        <div class="service-features">
                            <span class="feature-tag">Strategy</span>
                            <span class="feature-tag">Analysis</span>
                            <span class="feature-tag">Planning</span>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="service-card">
                        <div class="service-icon">
                            <i class="fas fa-tools"></i>
                        </div>
                        <h3 class="service-title">O&M Services</h3>
                        <p class="service-description">
                            Comprehensive operations and maintenance services ensuring optimal
                            performance and longevity of renewable energy assets.
                        </p>
                        <div class="service-features">
                            <span class="feature-tag">Maintenance</span>
                            <span class="feature-tag">Monitoring</span>
                            <span class="feature-tag">Optimization</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Key Statistics Section -->
    <section id="key-statistics" class="statistics-section">
        <div class="container">
            <div class="section-header-modern text-center">
                <div class="section-badge-modern">
                    <i class="fas fa-chart-bar"></i>
                    <span>Our Impact</span>
                </div>
                <h2 class="section-title-modern">
                    Measurable <span class="text-accent">Results</span>
                </h2>
                <p class="section-description-modern">
                    Two decades of dedication have resulted in significant achievements
                    that demonstrate our commitment to a sustainable future.
                </p>
            </div>

            <div class="row">
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="stat-card-modern">
                        <div class="stat-icon-modern">
                            <i class="fas fa-bolt"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number-modern" data-target="25">0</div>
                            <div class="stat-unit">GW+</div>
                            <h4 class="stat-title-modern">Installed Capacity</h4>
                            <p class="stat-description-modern">Clean energy capacity deployed worldwide</p>
                        </div>
                        <div class="stat-progress">
                            <div class="progress-bar" data-width="85%"></div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="stat-card-modern">
                        <div class="stat-icon-modern">
                            <i class="fas fa-globe-americas"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number-modern" data-target="30">0</div>
                            <div class="stat-unit">+</div>
                            <h4 class="stat-title-modern">Countries Served</h4>
                            <p class="stat-description-modern">Global presence across continents</p>
                        </div>
                        <div class="stat-progress">
                            <div class="progress-bar" data-width="75%"></div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="stat-card-modern">
                        <div class="stat-icon-modern">
                            <i class="fas fa-home"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number-modern" data-target="60">0</div>
                            <div class="stat-unit">M+</div>
                            <h4 class="stat-title-modern">Homes Powered</h4>
                            <p class="stat-description-modern">Families benefiting from clean energy</p>
                        </div>
                        <div class="stat-progress">
                            <div class="progress-bar" data-width="90%"></div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="stat-card-modern">
                        <div class="stat-icon-modern">
                            <i class="fas fa-leaf"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number-modern" data-target="95">0</div>
                            <div class="stat-unit">M</div>
                            <h4 class="stat-title-modern">Tons CO₂ Prevented</h4>
                            <p class="stat-description-modern">Annual carbon emissions avoided</p>
                        </div>
                        <div class="stat-progress">
                            <div class="progress-bar" data-width="95%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>



    <!-- Global Impact Showcase Section -->
    <section id="global-impact" class="global-impact-section">
        <div class="impact-background">
            <div class="impact-bg-image">
                <img src="https://images.unsplash.com/photo-1569163139394-de4e4f43e4e3?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80" alt="Global Impact" class="bg-image">
            </div>
            <div class="impact-overlay"></div>
        </div>
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mx-auto text-center">
                    <div class="impact-content">
                        <div class="section-badge-modern light">
                            <i class="fas fa-earth-americas"></i>
                            <span>Global Impact</span>
                        </div>
                        <h2 class="section-title-modern light">
                            Powering a <span class="text-gradient-light">Sustainable Future</span>
                        </h2>
                        <p class="section-description-modern light">
                            Our commitment extends beyond energy generation. We're building a legacy of
                            environmental stewardship, economic growth, and social responsibility that
                            benefits communities worldwide.
                        </p>
                    </div>
                </div>
            </div>

            <div class="row impact-cards-row justify-content-center">
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="impact-card-modern">
                        <div class="impact-card-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <h3 class="impact-card-title">Social</h3>
                        <p class="impact-card-description">
                            Creating 15,000+ jobs and empowering local communities through skills
                            development and sustainable economic opportunities.
                        </p>
                        <div class="impact-metrics">
                            <div class="metric">
                                <span class="metric-number">15K+</span>
                                <span class="metric-label">Jobs Created</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="impact-card-modern">
                        <div class="impact-card-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <h3 class="impact-card-title">Economic</h3>
                        <p class="impact-card-description">
                            Driving $50B+ in economic value through infrastructure investment,
                            energy cost savings, and sustainable development initiatives.
                        </p>
                        <div class="impact-metrics">
                            <div class="metric">
                                <span class="metric-number">$50B+</span>
                                <span class="metric-label">Economic Value</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="impact-card-modern">
                        <div class="impact-card-icon">
                            <i class="fas fa-leaf"></i>
                        </div>
                        <h3 class="impact-card-title">Environmental</h3>
                        <p class="impact-card-description">
                            Protecting ecosystems and promoting biodiversity through sustainable
                            energy practices and comprehensive environmental stewardship.
                        </p>
                        <div class="impact-metrics">
                            <div class="metric">
                                <span class="metric-number">500K+</span>
                                <span class="metric-label">Trees Equivalent</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>


        </div>
    </section>

    <?php include 'footer.php'; ?>

    <script>
        // Modern About Page Animations and Interactions
        document.addEventListener('DOMContentLoaded', function() {
            // Wait a bit to avoid conflicts with main.js
            setTimeout(function() {
                // Animated Counter for Statistics
                const counters = document.querySelectorAll('.stat-number-modern');
                const observerOptions = {
                    threshold: 0.3,
                    rootMargin: '0px 0px -50px 0px'
                };

            const counterObserver = new IntersectionObserver(function(entries) {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const counter = entry.target;
                        const target = parseInt(counter.getAttribute('data-target'));

                        // Ensure we have a valid target and add debugging
                        if (target && target > 0) {
                            console.log('Animating counter to:', target);
                            const duration = 2000;
                            const increment = target / (duration / 16);
                            let current = 0;

                            const updateCounter = () => {
                                if (current < target) {
                                    current += increment;
                                    counter.textContent = Math.ceil(current);
                                    requestAnimationFrame(updateCounter);
                                } else {
                                    counter.textContent = target;
                                }
                            };

                            // Small delay before starting animation
                            setTimeout(updateCounter, 300);
                            counterObserver.unobserve(counter);
                        } else {
                            console.log('Invalid target for counter:', counter, 'data-target:', counter.getAttribute('data-target'));
                        }
                    }
                });
            }, observerOptions);

            counters.forEach(counter => {
                counterObserver.observe(counter);
            });

            // Progress Bar Animation
            const progressBars = document.querySelectorAll('.progress-bar');
            const progressObserver = new IntersectionObserver(function(entries) {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const progressBar = entry.target;
                        const width = progressBar.getAttribute('data-width');
                        setTimeout(() => {
                            progressBar.style.width = width;
                        }, 500);
                        progressObserver.unobserve(progressBar);
                    }
                });
            }, observerOptions);

            progressBars.forEach(bar => {
                progressObserver.observe(bar);
            });

            // Smooth Scroll for Hero Actions (About page specific)
            document.querySelectorAll('.hero-actions-modern a[href^="#"], .cta-actions a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        const headerHeight = document.querySelector('.header-nav')?.offsetHeight || 80;
                        const targetPosition = target.offsetTop - headerHeight;
                        window.scrollTo({
                            top: targetPosition,
                            behavior: 'smooth'
                        });
                    }
                });
            });

            // Hero Scroll Indicator Animation
            const scrollIndicator = document.querySelector('.hero-scroll-indicator');
            if (scrollIndicator) {
                setInterval(() => {
                    scrollIndicator.style.transform = 'translateY(10px)';
                    setTimeout(() => {
                        scrollIndicator.style.transform = 'translateY(0)';
                    }, 500);
                }, 2000);
            }

            console.log('Modern About page loaded successfully with enhanced interactions!');
            }, 500); // Delay to avoid conflicts with main.js
        });
    </script>
