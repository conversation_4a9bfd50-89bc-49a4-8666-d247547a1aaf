/* Clean & Simple CSS for Eldergrove Energy Website */

:root {
    --primary-color: #22c55e;
    --secondary-color: #3b82f6;
    --dark-color: #1f2937;
    --light-color: #f9fafb;
    --text-color: #374151;
    --text-light: #6b7280;
    --text-lighter: #9ca3af;
    --white: #ffffff;
    --border-light: #e5e7eb;
    --gradient: linear-gradient(135deg, #22c55e 0%, #3b82f6 100%);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    overflow-x: hidden;
}

/* Header Styles */
.header-nav {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    border-bottom: 1px solid var(--border-light);
}

.fixed-top {
    position: fixed !important;
    top: 0;
    right: 0;
    left: 0;
    z-index: 1030;
}

.navbar {
    padding: 1rem 0;
    background-color: white !important;
}

.navbar-light .navbar-nav .nav-link {
    color: var(--text-color) !important;
}

.navbar-light .navbar-nav .nav-link:hover {
    color: var(--primary-color) !important;
}

.navbar-light .navbar-toggler {
    border-color: var(--border-light);
}

.navbar-light .navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%2833, 37, 41, 0.75%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

.brand-logo {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--primary-color);
    text-decoration: none;
}

.navbar-toggler {
    border: none;
    padding: 0.25rem 0.5rem;
    background: transparent;
}

.navbar-toggler:focus {
    box-shadow: none;
}

.navbar-toggler i {
    color: var(--text-color);
    font-size: 1.25rem;
}

.navbar-nav .nav-link {
    color: var(--text-color);
    font-weight: 500;
    margin: 0 0.5rem;
    padding: 0.5rem 0.75rem;
    transition: color 0.3s ease;
    text-decoration: none;
    border-radius: 5px;
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link:focus {
    color: var(--primary-color);
    background: var(--light-color);
}

.btn-cta {
    background: var(--primary-color);
    color: var(--white);
    padding: 0.5rem 1.5rem;
    border-radius: 25px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    margin-left: 1rem;
    display: inline-block;
}

.btn-cta:hover {
    background: #16a34a;
    color: var(--white);
    transform: translateY(-1px);
}

/* Navigation Dropdown Styles */
.navbar-nav .dropdown-menu {
    background: var(--white);
    border: 1px solid var(--border-light);
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    padding: 0.5rem 0;
    margin-top: 0.5rem;
    min-width: 200px;
}

.navbar-nav .dropdown-item {
    color: var(--text-color);
    padding: 0.5rem 1.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
    border-radius: 0;
}

.navbar-nav .dropdown-item:hover,
.navbar-nav .dropdown-item:focus {
    background: var(--light-color);
    color: var(--primary-color);
}

.navbar-nav .dropdown-toggle::after {
    margin-left: 0.5rem;
    vertical-align: 0.125em;
}

.navbar-nav .dropdown-toggle:hover::after {
    border-top-color: var(--primary-color);
}

/* Navigation CTA Button */
.nav-cta {
    background: var(--primary-color) !important;
    color: var(--white) !important;
    padding: 0.5rem 1.5rem !important;
    border-radius: 25px !important;
    font-weight: 600 !important;
    text-decoration: none !important;
    transition: all 0.3s ease !important;
    border: none !important;
}

.nav-cta:hover {
    background: #16a34a !important;
    color: var(--white) !important;
    transform: translateY(-1px) !important;
}

/* Hero Section */
.hero-section {
    min-height: 100vh;
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 140px 0 80px;
    position: relative;
}

.hero-clean-slider {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    min-height: 100vh;
}

/* Hero Slider */
.hero-slider {
    position: relative;
    overflow: hidden;
}

.hero-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 0.8s ease-in-out;
    z-index: 1;
    display: flex;
    align-items: center;
    padding: 140px 0 80px;
}

.hero-slide.active {
    opacity: 1;
    z-index: 2;
}

.hero-main-title {
    font-size: 3.5rem;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 1.5rem;
    color: #2c3e50;
}

.hero-description {
    font-size: 1.25rem;
    line-height: 1.6;
    color: #6c757d;
    margin-bottom: 2.5rem;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.hero-stats-cards {
    display: flex;
    justify-content: center;
    gap: 1.5rem;
    flex-wrap: wrap;
}

.stat-card {
    background: white;
    border-radius: 16px;
    padding: 2rem 1.5rem;
    text-align: center;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    min-width: 160px;
    flex: 1;
    max-width: 200px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.stat-number {
    display: block;
    font-size: 2.5rem;
    font-weight: 700;
    color: #28a745;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.9rem;
    color: #6c757d;
    font-weight: 500;
}

.text-gradient {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Mega Menu Styles */
.mega-dropdown .dropdown-menu {
    position: static;
}

.mega-menu {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    width: 100vw;
    max-width: none;
    border: none;
    border-radius: 0;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    padding: 2rem 0;
    margin: 0;
    background: white;
    z-index: 1000;
}

.mega-menu-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--primary-color);
}

.mega-menu-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.mega-menu-list li {
    margin-bottom: 0.5rem;
}

.mega-menu-list a {
    color: var(--text-color);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
    display: block;
    padding: 0.25rem 0;
}

.mega-menu-list a:hover {
    color: var(--primary-color);
}

.mega-menu-desc {
    color: var(--text-light);
    font-size: 0.9rem;
    line-height: 1.6;
}

.mega-menu-featured {
    position: relative;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.mega-menu-img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.mega-menu-featured-content {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    color: white;
    padding: 1.5rem;
}

.mega-menu-featured-content h6 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.mega-menu-featured-content p {
    font-size: 0.9rem;
    margin-bottom: 1rem;
    opacity: 0.9;
}

.mega-menu-cta {
    background: var(--light-color);
    padding: 1.5rem;
    border-radius: 12px;
    text-align: center;
}

.mega-menu-cta h6 {
    color: var(--dark-color);
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.mega-menu-cta p {
    color: var(--text-light);
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    border-radius: 20px;
}

/* Animated Text Slider */
.animated-text {
    position: relative;
    display: inline-block;
    height: 1.2em;
    overflow: hidden;
    vertical-align: baseline;
}

.text-item {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    opacity: 0;
    transform: translateY(100%);
    transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    white-space: nowrap;
}

.text-item.active {
    opacity: 1;
    transform: translateY(0);
}

.text-item:nth-child(1) {
    animation: textSlide1 9s infinite;
}

.text-item:nth-child(2) {
    animation: textSlide2 9s infinite;
}

.text-item:nth-child(3) {
    animation: textSlide3 9s infinite;
}

@keyframes textSlide1 {
    0%, 30% {
        opacity: 1;
        transform: translateY(0);
    }
    33%, 100% {
        opacity: 0;
        transform: translateY(-100%);
    }
}

@keyframes textSlide2 {
    0%, 30% {
        opacity: 0;
        transform: translateY(100%);
    }
    33%, 63% {
        opacity: 1;
        transform: translateY(0);
    }
    66%, 100% {
        opacity: 0;
        transform: translateY(-100%);
    }
}

@keyframes textSlide3 {
    0%, 63% {
        opacity: 0;
        transform: translateY(100%);
    }
    66%, 96% {
        opacity: 1;
        transform: translateY(0);
    }
    99%, 100% {
        opacity: 0;
        transform: translateY(-100%);
    }
}

/* Hero Section with Background Image */
.hero-with-bg {
    position: relative;
    overflow: hidden;
}

.hero-background-image {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

.hero-bg-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    opacity: 0.1;
}

.hero-bg-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(31, 41, 55, 0.6);
}

.hero-with-bg .container {
    position: relative;
    z-index: 2;
}

/* Hero Text Styles for Background Version */
.hero-title-white {
    color: var(--white) !important;
}

.hero-description-white {
    color: rgba(255, 255, 255, 0.95) !important;
}

.text-white {
    color: var(--white) !important;
}

.hero-with-bg .hero-title {
    color: var(--white) !important;
}

.hero-with-bg .hero-description {
    color: rgba(255, 255, 255, 0.95) !important;
}

.hero-with-bg .stat-item .stat-number {
    color: var(--white) !important;
}

.hero-with-bg .stat-item .stat-label {
    color: rgba(255, 255, 255, 0.9) !important;
}

.hero-with-bg .hero-badge {
    background: rgba(255, 255, 255, 0.2) !important;
    color: var(--white) !important;
    backdrop-filter: blur(10px);
}

/* Hero Slider Controls */
.hero-slider-controls {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 15px;
    z-index: 10;
}

.slider-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.5);
    cursor: pointer;
    transition: all 0.3s ease;
}

.slider-dot.active {
    background: var(--white);
    transform: scale(1.2);
}

.slider-dot:hover {
    background: rgba(255, 255, 255, 0.8);
}

/* Navigation Arrows */
.slider-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: var(--white);
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 10;
    backdrop-filter: blur(10px);
}

.slider-nav:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    color: var(--white);
    transform: translateY(-50%) scale(1.1);
}

.slider-prev {
    left: 30px;
}

.slider-next {
    right: 30px;
}

.hero-content {
    padding: 2rem 0;
}

.hero-badge {
    display: inline-block;
    background: var(--light-color);
    color: var(--primary-color);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: 1rem;
    color: var(--dark-color);
}

.hero-subscription {
    font-size: 1.125rem;
    font-weight: 500;
    margin-bottom: 1.5rem;
    opacity: 0.9;
    font-style: italic;
}

.text-gradient {
    background: var(--gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-description {
    font-size: 1.25rem;
    color: var(--text-light);
    margin-bottom: 2rem;
    line-height: 1.6;
}

.hero-description-white {
    color: rgba(255, 255, 255, 0.95) !important;
}

.hero-stats {
    display: flex;
    gap: 2rem;
    margin-bottom: 2rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
}

.stat-label {
    font-size: 0.875rem;
    color: var(--text-light);
}

.hero-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    margin-top: 2rem;
}

.hero-actions.text-center {
    text-align: center;
}

.btn-primary {
    background: #28a745;
    color: var(--white);
    padding: 14px 32px;
    border-radius: 50px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    display: inline-block;
    margin: 0 8px;
    border: none;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.btn-primary:hover {
    background: #218838;
    color: var(--white);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(40, 167, 69, 0.4);
    text-decoration: none;
}

.btn-outline {
    background: transparent;
    color: #28a745;
    border: 2px solid #28a745;
    padding: 12px 30px;
    border-radius: 50px;
    font-weight: 600;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
    margin: 0 8px;
}

.btn-outline:hover {
    background: #28a745;
    color: var(--white);
    transform: translateY(-2px);
    text-decoration: none;
}

.btn-secondary {
    color: var(--white);
    background: var(--secondary-color);
    padding: 0.75rem 2rem;
    border-radius: 25px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    border: 1px solid var(--secondary-color);
}

.btn-secondary:hover {
    color: var(--white);
    background: #2563eb;
    border-color: #2563eb;
    transform: translateY(-1px);
}

/* Hero Visual */
.hero-visual {
    position: relative;
    height: 500px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.wind-turbine-graphic {
    position: relative;
    width: 200px;
    height: 400px;
}

.turbine-base {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 20px;
    height: 20px;
    background: var(--text-light);
    border-radius: 50%;
}

.turbine-tower {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    width: 4px;
    height: 300px;
    background: var(--text-light);
}

.turbine-nacelle {
    position: absolute;
    top: 80px;
    left: 50%;
    transform: translateX(-50%);
    width: 30px;
    height: 15px;
    background: var(--primary-color);
    border-radius: 10px;
}

.turbine-blades {
    position: absolute;
    top: 70px;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 40px;
    animation: rotate 4s linear infinite;
    transform-origin: center center;
}

.blade {
    position: absolute;
    width: 2px;
    height: 60px;
    background: var(--primary-color);
    border-radius: 2px;
    transform-origin: bottom center;
}

.blade-1 { transform: rotate(0deg); }
.blade-2 { transform: rotate(120deg); }
.blade-3 { transform: rotate(240deg); }

@keyframes rotate {
    0% { transform: translateX(-50%) rotate(0deg); }
    100% { transform: translateX(-50%) rotate(360deg); }
}

.floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.element {
    position: absolute;
    width: 40px;
    height: 40px;
    background: var(--light-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    animation: float 4s ease-in-out infinite;
}

.element-1 {
    top: 20%;
    right: 20%;
    animation-delay: 0s;
}

.element-2 {
    bottom: 30%;
    left: 10%;
    animation-delay: 1s;
}

.element-3 {
    top: 60%;
    right: 40%;
    animation-delay: 2s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* Hero Image Slider */
.hero-slider {
    position: relative;
    overflow: hidden;
    min-height: 100vh;
    display: flex;
    align-items: center;
}

.hero-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 1s ease-in-out;
    display: flex;
    align-items: center;
    min-height: 100vh;
    padding: 140px 0 80px;
}

.hero-slide.active {
    opacity: 1;
    position: relative;
}

.hero-slide-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

.hero-slide-bg-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    opacity: 0.3;
}

.hero-slide-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.8) 0%, rgba(32, 201, 151, 0.6) 100%);
}

.hero-slide .container {
    position: relative;
    z-index: 2;
}

.btn-outline-white {
    background: transparent;
    color: white;
    border: 2px solid white;
    padding: 14px 32px;
    border-radius: 50px;
    font-weight: 600;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
    margin: 0 8px;
}

.btn-outline-white:hover {
    background: white;
    color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 255, 255, 0.3);
}

.hero-slider-controls {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 0.5rem;
    z-index: 3;
}

.slider-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.5);
    cursor: pointer;
    transition: all 0.3s ease;
}

.slider-dot.active {
    background: var(--white);
    transform: scale(1.2);
}

.slider-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(255, 255, 255, 0.2);
    color: var(--white);
    border: none;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 1.25rem;
    transition: all 0.3s ease;
    z-index: 3;
    backdrop-filter: blur(10px);
}

.slider-nav:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-50%) scale(1.1);
}

.slider-prev {
    left: 2rem;
}

.slider-next {
    right: 2rem;
}

/* Section Styles */
.section-header {
    margin-bottom: 4rem;
}

.section-badge {
    display: inline-block;
    background: var(--light-color);
    color: var(--primary-color);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 1rem;
}

.section-subtitle {
    font-size: 1.125rem;
    color: var(--text-light);
    max-width: 600px;
    margin: 0 auto;
}

/* About Section */
.about-section {
    padding: 5rem 0;
    background: var(--light-color);
}

.about-content {
    padding: 2rem 0;
}

.about-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 2rem;
}

.about-icon {
    width: 60px;
    height: 60px;
    background: var(--white);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1.5rem;
    flex-shrink: 0;
}

.about-icon i {
    font-size: 1.5rem;
    color: var(--primary-color);
}

.about-text h4 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

.about-text p {
    color: var(--text-light);
    line-height: 1.6;
}

.about-visual {
    position: relative;
}

.about-image {
    position: relative;
    border-radius: 15px;
    overflow: hidden;
}

.about-image img {
    width: 100%;
    height: 400px;
    object-fit: cover;
}

.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(34, 197, 94, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.about-image:hover .image-overlay {
    opacity: 1;
}

.overlay-content {
    text-align: center;
    color: var(--white);
}

.overlay-content h5 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.overlay-content p {
    font-size: 1.125rem;
}

/* Legacy Services Section - Renamed to avoid conflicts */
.legacy-services-section {
    padding: 5rem 0;
    background: var(--white);
}

.legacy-service-card {
    background: var(--white);
    padding: 2rem;
    border-radius: 15px;
    border: 1px solid var(--border-light);
    transition: all 0.3s ease;
    height: 100%;
}

.legacy-service-card:hover {
    transform: translateY(-5px);
    border-color: var(--primary-color);
}

.legacy-service-icon {
    width: 60px;
    height: 60px;
    background: var(--light-color);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
}

.legacy-service-icon i {
    font-size: 1.5rem;
    color: var(--primary-color);
}

.legacy-service-card h4 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 1rem;
}

.legacy-service-card p {
    color: var(--text-light);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.legacy-service-features {
    list-style: none;
    padding: 0;
}

.legacy-service-features li {
    color: var(--text-light);
    margin-bottom: 0.5rem;
    position: relative;
    padding-left: 1.5rem;
}

.legacy-service-features li:before {
    content: "✓";
    position: absolute;
    left: 0;
    color: var(--primary-color);
    font-weight: 600;
}

/* Projects Section */
.projects-section {
    padding: 5rem 0;
    background: var(--light-color);
}

.project-card {
    background: var(--white);
    border-radius: 15px;
    overflow: hidden;
    transition: all 0.3s ease;
    height: 100%;
    border: 1px solid var(--border-light);
}

.project-card:hover {
    transform: translateY(-5px);
    border-color: var(--primary-color);
}

.project-image {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.project-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.project-card:hover .project-image img {
    transform: scale(1.05);
}

.project-overlay {
    position: absolute;
    top: 1rem;
    right: 1rem;
}

.project-status {
    background: var(--primary-color);
    color: var(--white);
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.75rem;
    font-weight: 600;
}

.project-status.under-construction {
    background: #f59e0b;
}

.project-status.planning {
    background: var(--secondary-color);
}

.project-content {
    padding: 1.5rem;
}

.project-content h4 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 1rem;
}

.project-specs {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.project-specs span {
    font-size: 0.875rem;
    color: var(--text-light);
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.project-specs i {
    color: var(--primary-color);
}

.project-content p {
    color: var(--text-light);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.project-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: gap 0.3s ease;
}

.project-link:hover {
    gap: 0.75rem;
    color: var(--primary-color);
}

/* Technology Section */
.technology-section {
    padding: 5rem 0;
    background: var(--white);
}

.technology-content {
    padding: 2rem 0;
}

.tech-feature {
    display: flex;
    align-items: flex-start;
    margin-bottom: 2rem;
}

.tech-icon {
    width: 60px;
    height: 60px;
    background: var(--light-color);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1.5rem;
    flex-shrink: 0;
}

.tech-icon i {
    font-size: 1.5rem;
    color: var(--primary-color);
}

.tech-info h4 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

.tech-info p {
    color: var(--text-light);
    line-height: 1.6;
}

.technology-visual {
    position: relative;
}

.technology-visual img {
    width: 100%;
    height: 400px;
    object-fit: cover;
    border-radius: 15px;
}

.tech-stats {
    position: absolute;
    bottom: 2rem;
    left: 2rem;
    display: flex;
    gap: 2rem;
}

.tech-stat {
    background: rgba(255, 255, 255, 0.9);
    padding: 1rem;
    border-radius: 10px;
    text-align: center;
    backdrop-filter: blur(10px);
}

.stat-value {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
}

.stat-desc {
    font-size: 0.875rem;
    color: var(--text-light);
/* Technology Feature Button */
.tech-feature-button {
    padding: 1rem 0;
    margin-top: 1rem;
}

.tech-feature-button .btn-primary {
    padding: 1rem 2rem;
    font-size: 1.125rem;
    font-weight: 600;
    border-radius: 30px;
    box-shadow: 0 4px 15px rgba(34, 197, 94, 0.2);
    transition: all 0.3s ease;
}

.tech-feature-button .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(34, 197, 94, 0.3);
}

.tech-feature-button .btn-primary i {
    margin-right: 0.5rem;
}
}

/* Sustainability Section */
.sustainability-section {
    padding: 5rem 0;
    background: var(--light-color);
}

.sustainability-card {
    background: var(--white);
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    border: 1px solid var(--border-light);
    transition: all 0.3s ease;
    height: 100%;
}

.sustainability-card:hover {
    transform: translateY(-5px);
    border-color: var(--primary-color);
}

.sustain-icon {
    width: 80px;
    height: 80px;
    background: var(--light-color);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

.sustain-icon i {
    font-size: 2rem;
    color: var(--primary-color);
}

.sustainability-card h4 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 1rem;
}

.sustainability-card p {
    color: var(--text-light);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.sustain-metric {
    padding-top: 1rem;
    border-top: 1px solid var(--border-light);
}

.metric-value {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
}

.metric-label {
    font-size: 0.875rem;
    color: var(--text-light);
}

/* News Section */
.news-section {
    padding: 5rem 0;
    background: var(--white);
}

/* Additional Hero Section */
.additional-hero-section {
    position: relative;
    min-height: 50vh;
    display: flex;
    align-items: center;
    overflow: hidden;
    padding: 80px 0 80px;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

.hero-bg-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(31, 41, 55, 0.7);
}

.additional-hero-section .container {
    position: relative;
    z-index: 2;
}

.additional-hero-section .hero-content {
    color: var(--white) !important;
    text-align: center;
}

.additional-hero-section .hero-title {
    color: var(--white) !important;
}

.additional-hero-section .hero-description {
    color: rgba(255, 255, 255, 0.95) !important;
}

.additional-hero-section .hero-badge {
    background: rgba(255, 255, 255, 0.2) !important;
    color: var(--white) !important;
    backdrop-filter: blur(10px);
}

.additional-hero-section .hero-badge {
    background: rgba(255, 255, 255, 0.2);
    color: var(--white);
    backdrop-filter: blur(10px);
}

.additional-hero-section .hero-title {
    color: var(--white);
    font-size: 3rem;
    margin-bottom: 1.5rem;
}

.additional-hero-section .text-gradient {
    background: linear-gradient(135deg, #22c55e 0%, #60a5fa 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.additional-hero-section .hero-description {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1.25rem;
    margin-bottom: 2rem;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.additional-hero-section .hero-actions {
    justify-content: center;
}

/* Impact Hero Section */
.impact-hero-section {
    position: relative;
    min-height: 50vh;
    display: flex;
    align-items: center;
    overflow: hidden;
    padding: 80px 0 80px;
}

.impact-hero-section .hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

.impact-hero-section .hero-bg-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.impact-hero-section .hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(31, 41, 55, 0.6);
}

.impact-hero-section .container {
    position: relative;
    z-index: 2;
}

.impact-hero-section .hero-content {
    color: var(--white) !important;
    text-align: center;
}

.impact-hero-section .hero-badge {
    background: rgba(255, 255, 255, 0.2) !important;
    color: var(--white) !important;
    backdrop-filter: blur(10px);
}

.impact-hero-section .hero-title {
    color: var(--white) !important;
    font-size: 3rem;
    margin-bottom: 1.5rem;
}

.impact-hero-section .text-gradient {
    background: linear-gradient(135deg, #22c55e 0%, #60a5fa 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.impact-hero-section .hero-description {
    color: rgba(255, 255, 255, 0.95) !important;
    font-size: 1.25rem;
    margin-bottom: 2rem;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

/* Testimonials Section */
.testimonials-section {
    padding: 5rem 0;
    background: var(--white);
    font-family: 'Poppins', sans-serif;
}

.testimonial-card {
    background: var(--white);
    border-radius: 15px;
    padding: 2rem;
    border: 1px solid var(--border-light);
    transition: all 0.3s ease;
    height: 100%;
    position: relative;
    font-family: 'Poppins', sans-serif;
}

.testimonial-card:hover {
    transform: translateY(-5px);
    border-color: var(--primary-color);
    box-shadow: 0 10px 30px rgba(34, 197, 94, 0.1);
}

.testimonial-content {
    margin-bottom: 2rem;
}

.quote-icon {
    width: 40px;
    height: 40px;
    background: var(--light-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
}

.quote-icon i {
    color: var(--primary-color);
    font-size: 1.25rem;
}

.testimonial-content p {
    color: var(--text-color);
    font-style: italic;
    line-height: 1.6;
    font-size: 0.95rem;
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.author-image {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid var(--light-color);
}

.author-info h5 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.25rem;
}

.author-info span {
    color: var(--text-light);
    font-size: 0.875rem;
}

/* Global Presence Section */
.global-presence-section {
    padding: 5rem 0;
    background: var(--light-color);
}

/* Global Impact Cards */
.global-impact-card {
    background: var(--white);
    padding: 2.5rem 2rem;
    border-radius: 15px;
    border: 1px solid var(--border-light);
    transition: all 0.3s ease;
    text-align: center;
    height: 100%;
}

.global-impact-card:hover {
    transform: translateY(-5px);
    border-color: var(--primary-color);
    box-shadow: 0 10px 30px rgba(34, 197, 94, 0.1);
}

.impact-icon {
    width: 80px;
    height: 80px;
    background: var(--light-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

.impact-icon i {
    font-size: 2rem;
    color: var(--primary-color);
}

.impact-number {
    font-size: 3rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.impact-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 1rem;
}

.impact-description {
    color: var(--text-light);
    font-size: 0.95rem;
    margin: 0;
}

.global-stats-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    max-width: 900px;
    margin: 0 auto;
}

.stat-item-large {
    background: var(--white);
    padding: 2rem;
    border-radius: 15px;
    border: 1px solid var(--border-light);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.stat-item-large:hover {
    transform: translateY(-5px);
    border-color: var(--primary-color);
    box-shadow: 0 10px 30px rgba(34, 197, 94, 0.1);
}

.stat-item-large .stat-icon {
    width: 60px;
    height: 60px;
    background: var(--light-color);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.stat-item-large .stat-icon i {
    font-size: 1.5rem;
    color: var(--primary-color);
}

.stat-content .stat-number {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.25rem;
}

.stat-content .stat-label {
    display: block;
    font-size: 1rem;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

.stat-content .stat-description {
    font-size: 0.875rem;
    color: var(--text-light);
    margin: 0;
}



.news-card {
    background: var(--white);
    border-radius: 15px;
    overflow: hidden;
    border: 1px solid var(--border-light);
    transition: all 0.3s ease;
    height: 100%;
}

.news-card:hover {
    transform: translateY(-5px);
    border-color: var(--primary-color);
}

.news-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.news-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.news-card:hover .news-image img {
    transform: scale(1.05);
}

.news-date {
    position: absolute;
    top: 1rem;
    left: 1rem;
    background: var(--white);
    padding: 0.5rem;
    border-radius: 10px;
    text-align: center;
    min-width: 50px;
}

.date-day {
    display: block;
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--primary-color);
}

.date-month {
    font-size: 0.75rem;
    color: var(--text-light);
    text-transform: uppercase;
}

.news-content {
    padding: 1.5rem;
}

.news-category {
    background: var(--light-color);
    color: var(--primary-color);
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.75rem;
    font-weight: 600;
    display: inline-block;
    margin-bottom: 1rem;
}

.news-content h4 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 1rem;
    line-height: 1.4;
}

.news-content p {
    color: var(--text-light);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.news-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: gap 0.3s ease;
}

.news-link:hover {
    gap: 0.75rem;
    color: var(--primary-color);
}

/* Contact Section */
.contact-section {
    padding: 5rem 0;
    background: var(--light-color);
}

.contact-card {
    background: var(--white);
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    border: 1px solid var(--border-light);
    transition: all 0.3s ease;
    height: 100%;
}

.contact-card:hover {
    transform: translateY(-5px);
    border-color: var(--primary-color);
}

.contact-icon {
    width: 60px;
    height: 60px;
    background: var(--light-color);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

.contact-icon i {
    font-size: 1.5rem;
    color: var(--primary-color);
}

.contact-card h4 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 1rem;
}

.contact-card p {
    color: var(--text-light);
    margin-bottom: 0.5rem;
}

/* Contact Info Card */
.contact-info-card {
    background: var(--white);
    padding: 2rem;
    border-radius: 15px;
    border: 1px solid var(--border-light);
    transition: all 0.3s ease;
    height: 100%;
}

.contact-info-card:hover {
    transform: translateY(-5px);
    border-color: var(--primary-color);
    box-shadow: 0 10px 30px rgba(34, 197, 94, 0.1);
}

.contact-info-card h4 {
    color: var(--dark-color);
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 1rem;
    margin-top: 1.5rem;
    display: flex;
    align-items: center;
}

.contact-info-card h4:first-child {
    margin-top: 0;
}

.contact-info-card h4 i {
    color: var(--primary-color);
}

.contact-info-card p {
    color: var(--text-light);
    margin-bottom: 0.5rem;
    line-height: 1.6;
}

.contact-info-card p:last-child {
    margin-bottom: 0;
}

.contact-info-card strong {
    color: var(--dark-color);
}

/* Contact Info Section */
.contact-info-section {
    padding: 2rem 0;
}

.contact-info-section h4 {
    color: var(--dark-color);
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
}

.contact-info-section h4 i {
    color: var(--primary-color);
}

.contact-details p {
    color: var(--text-light);
    margin-bottom: 0.5rem;
    line-height: 1.6;
}

.contact-details p:last-child {
    margin-bottom: 0;
}

.contact-details strong {
    color: var(--dark-color);
}

.contact-form {
    background: var(--white);
    padding: 2rem;
    border-radius: 15px;
    border: 1px solid var(--border-light);
}

.contact-form .form-control {
    border: 1px solid var(--border-light);
    border-radius: 10px;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.contact-form .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(34, 197, 94, 0.1);
}

.contact-form textarea.form-control {
    resize: vertical;
    min-height: 120px;
}

/* Footer */
.footer {
    background: var(--dark-color);
    color: var(--white);
    padding: 5rem 0 2rem;
}

.footer-brand h5 {
    color: var(--white);
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.footer-brand p {
    color: var(--text-lighter);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-links a {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-links a:hover {
    background: var(--primary-color);
    color: var(--white);
}

.footer-links h6 {
    color: var(--white);
    font-weight: 600;
    margin-bottom: 1rem;
}

.footer-links ul {
    list-style: none;
    padding: 0;
}

.footer-links ul li {
    margin-bottom: 0.5rem;
}

.footer-links ul li a {
    color: var(--text-lighter);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-links ul li a:hover {
    color: var(--primary-color);
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 2rem;
    margin-top: 2rem;
}

.footer-bottom p {
    color: var(--text-lighter);
    margin: 0;
}

.footer-bottom a {
    color: var(--text-lighter);
    text-decoration: none;
    margin-left: 1rem;
    transition: color 0.3s ease;
}

.footer-bottom a:hover {
    color: var(--primary-color);
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }

    .hero-description {
        font-size: 1.125rem;
    }

    .hero-stats {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .hero-actions {
        flex-direction: column;
        align-items: center;
    }

    .section-title {
        font-size: 2rem;
    }

    .section-subtitle {
        font-size: 1rem;
    }

    .about-item,
    .tech-feature {
        flex-direction: column;
        text-align: center;
    }

    .about-icon,
    .tech-icon {
        margin-right: 0;
        margin-bottom: 1rem;
    }

    .hero-visual {
        height: 300px;
        margin-top: 2rem;
    }

    .wind-turbine-graphic {
        width: 150px;
        height: 300px;
    }

    .tech-stats {
        position: static;
        justify-content: center;
        margin-top: 1rem;
    }

    .navbar-nav .nav-link {
        margin: 0.25rem 0;
        padding: 0.75rem 1rem;
        border-radius: 5px;
    }

    .btn-cta {
        margin-left: 0;
        margin-top: 1rem;
        display: block;
        text-align: center;
        width: fit-content;
        margin-left: auto;
        margin-right: auto;
    }

    .navbar-nav .dropdown-menu {
        border: none;
        box-shadow: none;
        background: var(--light-color);
        margin-top: 0;
        padding: 0;
        border-radius: 0;
    }

    .navbar-nav .dropdown-item {
        padding: 0.5rem 2rem;
        font-size: 0.9rem;
    }

    .navbar-collapse {
        background: var(--white);
        margin-top: 1rem;
        padding: 1rem;
        border-radius: 10px;
        border: 1px solid var(--border-light);
    }

    /* Hero Sections Mobile */
    .hero-section {
        min-height: 55vh;
        padding: 80px 0 30px;
    }

    .hero-main-title {
        font-size: 2.5rem;
    }

    .hero-description {
        font-size: 1.1rem;
    }

    .hero-stats-cards {
        gap: 1rem;
    }

    .stat-card {
        min-width: 150px;
        padding: 1.5rem 1rem;
    }

    .stat-number {
        font-size: 2rem;
    }

    .additional-hero-section {
        min-height: 45vh;
        padding: 50px 0 30px;
    }

    .impact-hero-section {
        min-height: 45vh;
        padding: 50px 0 30px;
    }

    .additional-hero-section .hero-title,
    .impact-hero-section .hero-title {
        font-size: 2.5rem;
    }

    .additional-hero-section .hero-description,
    .impact-hero-section .hero-description {
        font-size: 1.125rem;
    }

    /* Hero Slider Mobile */
    .hero-slider-controls {
        bottom: 20px;
        gap: 10px;
    }

    .slider-nav {
        width: 40px;
        height: 40px;
        font-size: 0.875rem;
    }

    .slider-prev {
        left: 15px;
    }

    .slider-next {
        right: 15px;
    }

    /* Global Presence Mobile */
    .global-stats-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .stat-item-large {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    /* Testimonials Mobile */
    .testimonial-author {
        flex-direction: column;
        text-align: center;
        gap: 0.75rem;
    }

    .testimonial-content p {
        font-size: 0.9rem;
    }
}

@media (max-width: 576px) {
    .hero-title {
        font-size: 2rem;
    }

    .section-title {
        font-size: 1.75rem;
    }

    .hero-stats {
        gap: 0.5rem;
    }

    .stat-number {
        font-size: 1.5rem;
    }

    /* Hero Sections Small Mobile */
    .hero-section {
        min-height: 50vh;
        padding: 60px 0 25px;
    }

    .hero-main-title {
        font-size: 2rem;
    }

    .hero-description {
        font-size: 1rem;
    }

    .hero-stats-cards {
        flex-direction: column;
        align-items: center;
        gap: 1rem;
    }

    .stat-card {
        width: 100%;
        max-width: 200px;
    }

    .additional-hero-section {
        min-height: 40vh;
        padding: 40px 0 25px;
    }

    .impact-hero-section {
        min-height: 40vh;
        padding: 40px 0 25px;
    }

    .additional-hero-section .hero-title,
    .impact-hero-section .hero-title {
        font-size: 2rem;
    }

    .additional-hero-section .hero-description,
    .impact-hero-section .hero-description {
        font-size: 1rem;
    }

    /* Global Presence Small Mobile */
    .stat-item-large {
        padding: 1.5rem;
    }

    .stat-content .stat-number {
        font-size: 1.75rem;
    }

    /* Testimonials Small Mobile */
    .testimonial-card {
        padding: 1.5rem;
    }

    .testimonial-content p {
        font-size: 0.85rem;
    }

    .author-image {
        width: 50px;
        height: 50px;
    }
}

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* Animations */
.fade-in {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}

/* Background Classes for Section Alternation */
.bg-white {
    background-color: var(--white) !important;
}

.bg-light {
    background-color: var(--light-color) !important;
}

/* Utility Classes */
.text-center {
    text-align: center;
}

.mb-4 {
    margin-bottom: 1.5rem;
}

.mb-5 {
    margin-bottom: 3rem;
}

.mt-5 {
    margin-top: 3rem;
}

.py-5 {
    padding: 3rem 0;
}

/* Focus States for Accessibility */
.btn-primary:focus,
.btn-secondary:focus,
.btn-cta:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

.form-control:focus {
    outline: none;
}

/* Print Styles */
@media print {
    .header-nav,
    .footer {
        display: none;
    }

    .hero-section {
        min-height: auto;
        padding: 2rem 0;
    }

    * {
        background: white !important;
        color: black !important;
    }
}

/* Modern About Page Styles */

/* Modern Hero Section */
.modern-hero-section {
    position: relative;
    min-height: 70vh;
    display: flex;
    align-items: center;
    overflow: hidden;
    background: linear-gradient(135deg, #1e3a8a 0%, #059669 100%);
}

.hero-background-wrapper {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

.hero-background-image {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.hero-background-image .hero-bg-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    opacity: 0.2;
}

.hero-gradient-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(30, 58, 138, 0.9) 0%, rgba(5, 150, 105, 0.8) 100%);
}

.hero-particles {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: radial-gradient(2px 2px at 20px 30px, rgba(255, 255, 255, 0.1), transparent),
                      radial-gradient(2px 2px at 40px 70px, rgba(255, 255, 255, 0.1), transparent),
                      radial-gradient(1px 1px at 90px 40px, rgba(255, 255, 255, 0.1), transparent);
    background-repeat: repeat;
    background-size: 100px 100px;
    animation: particleFloat 20s linear infinite;
}

@keyframes particleFloat {
    0% { transform: translateY(0px); }
    100% { transform: translateY(-100px); }
}

.modern-hero-section .container {
    position: relative;
    z-index: 2;
}

.hero-content-modern {
    text-align: center;
    color: white;
    padding: 2rem 0;
}

.hero-badge-modern {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 50px;
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: 2rem;
    transition: all 0.3s ease;
}

.hero-badge-modern:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.hero-title-modern {
    font-size: 4rem;
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: 1.5rem;
    color: white;
}

.text-gradient-modern {
    background: linear-gradient(135deg, #60a5fa 0%, #34d399 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-description-modern {
    font-size: 1.25rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 3rem;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.6;
}

.hero-actions-modern {
    display: flex;
    gap: 1.5rem;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    margin-bottom: 4rem;
}

.btn-modern-primary {
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
    color: white;
    padding: 1rem 2rem;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 4px 15px rgba(34, 197, 94, 0.3);
}

.btn-modern-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(34, 197, 94, 0.4);
    color: white;
    text-decoration: none;
}

.btn-modern-outline {
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    background: transparent;
    color: white;
    padding: 1rem 2rem;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50px;
    text-decoration: none;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.btn-modern-outline:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
    color: white;
    text-decoration: none;
}

.hero-scroll-indicator {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    color: rgba(255, 255, 255, 0.7);
    animation: bounce 2s infinite;
}

.scroll-arrow {
    font-size: 1.5rem;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateX(-50%) translateY(0); }
    40% { transform: translateX(-50%) translateY(-10px); }
    60% { transform: translateX(-50%) translateY(-5px); }
}

/* Company Introduction Section */
.company-intro-section {
    padding: 6rem 0;
    background: #f8fafc;
}

.section-badge-modern {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: #e0f2fe;
    color: #0369a1;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
}

.section-title-modern {
    font-size: 3rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.text-accent {
    color: #059669;
}

.intro-description {
    font-size: 1.125rem;
    color: #64748b;
    line-height: 1.7;
    margin-bottom: 3rem;
}

.intro-highlights {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.highlight-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.highlight-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    flex-shrink: 0;
}

.highlight-text h4 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 0.5rem;
}

.highlight-text p {
    color: #64748b;
    margin: 0;
    line-height: 1.6;
}

.intro-visual {
    position: relative;
}

.visual-card {
    position: relative;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.intro-image {
    width: 100%;
    height: 400px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.visual-card:hover .intro-image {
    transform: scale(1.05);
}

.visual-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.4);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.visual-card:hover .visual-overlay {
    opacity: 1;
}

.play-button {
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #059669;
    font-size: 2rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.play-button:hover {
    background: white;
    transform: scale(1.1);
}

.floating-stats {
    position: absolute;
    bottom: -20px;
    right: 20px;
    display: flex;
    gap: 1rem;
}

.floating-stat {
    background: white;
    padding: 1rem 1.5rem;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    text-align: center;
    min-width: 80px;
}

.floating-stat .stat-number {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: #059669;
    margin-bottom: 0.25rem;
}

.floating-stat .stat-label {
    font-size: 0.75rem;
    color: #64748b;
    font-weight: 600;
}

/* Services Section */
.services-section {
    padding: 6rem 0;
    background: white;
}

.section-header-modern {
    margin-bottom: 4rem;
}

.section-description-modern {
    font-size: 1.125rem;
    color: #64748b;
    max-width: 700px;
    margin: 0 auto 2rem;
    line-height: 1.7;
}

.service-card {
    background: white;
    border-radius: 20px;
    padding: 2.5rem 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    height: 100%;
    border: 1px solid #f1f5f9;
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    border-color: #22c55e;
}

.service-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
    margin-bottom: 1.5rem;
    transition: all 0.3s ease;
}

.service-card:hover .service-icon {
    transform: scale(1.1);
}

.service-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 1rem;
}

.service-description {
    color: #64748b;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.service-features {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.feature-tag {
    background: #f0fdf4;
    color: #16a34a;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.75rem;
    font-weight: 600;
    border: 1px solid #bbf7d0;
}

/* Statistics Section */
.statistics-section {
    padding: 6rem 0;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.stat-card-modern {
    background: white;
    border-radius: 20px;
    padding: 2.5rem 2rem;
    text-align: center;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-card-modern:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.stat-icon-modern {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
    margin: 0 auto 1.5rem;
    transition: all 0.3s ease;
}

.stat-card-modern:hover .stat-icon-modern {
    transform: scale(1.1);
}

.stat-content {
    margin-bottom: 1.5rem;
}

.stat-number-modern {
    font-size: 3.5rem;
    font-weight: 800;
    color: #1e293b;
    display: inline-block;
    margin-right: 0.25rem;
}

.stat-unit {
    font-size: 2rem;
    font-weight: 600;
    color: #059669;
}

.stat-title-modern {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 0.5rem;
}

.stat-description-modern {
    color: #64748b;
    font-size: 0.95rem;
    line-height: 1.5;
}

.stat-progress {
    height: 4px;
    background: #e2e8f0;
    border-radius: 2px;
    overflow: hidden;
    margin-top: 1rem;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
    width: 0;
    transition: width 1.5s ease-in-out;
    border-radius: 2px;
}

/* Journey Timeline Section */
.journey-section {
    padding: 6rem 0;
    background: white;
}

.journey-content {
    margin-bottom: 4rem;
}

.journey-visual {
    position: relative;
}

.journey-image {
    width: 100%;
    height: 400px;
    object-fit: cover;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.timeline-modern {
    position: relative;
    padding-left: 2rem;
}

.timeline-modern::before {
    content: '';
    position: absolute;
    left: 30px;
    top: 0;
    bottom: 0;
    width: 4px;
    background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
    border-radius: 2px;
}

.timeline-item-modern {
    position: relative;
    margin-bottom: 4rem;
    display: flex;
    align-items: flex-start;
    gap: 2rem;
}

.timeline-item-modern::before {
    content: '';
    position: absolute;
    left: -47px;
    top: 10px;
    width: 20px;
    height: 20px;
    background: #22c55e;
    border-radius: 50%;
    border: 4px solid white;
    box-shadow: 0 0 0 4px #22c55e;
    z-index: 2;
}

.timeline-year-modern {
    background: linear-gradient(135deg, #1e293b 0%, #374151 100%);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    font-weight: 700;
    font-size: 1.125rem;
    min-width: 100px;
    text-align: center;
    flex-shrink: 0;
}

.timeline-content-modern {
    flex: 1;
    background: #f8fafc;
    padding: 2rem;
    border-radius: 20px;
    border-left: 4px solid #22c55e;
    transition: all 0.3s ease;
}

.timeline-content-modern:hover {
    background: white;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transform: translateX(10px);
}

.timeline-icon-modern {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

.timeline-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 1rem;
}

.timeline-description {
    color: #64748b;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.timeline-stats {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.timeline-stat {
    font-size: 1.25rem;
    font-weight: 700;
    color: #22c55e;
}

.timeline-stat-label {
    font-size: 0.875rem;
    color: #64748b;
    font-weight: 600;
}

/* Global Impact Section */
.global-impact-section {
    position: relative;
    padding: 6rem 0;
    overflow: hidden;
}

.impact-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

.impact-bg-image {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.impact-bg-image .bg-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    opacity: 0.3;
}

.impact-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(30, 58, 138, 0.9) 0%, rgba(5, 150, 105, 0.8) 100%);
}

.global-impact-section .container {
    position: relative;
    z-index: 2;
}

.impact-content {
    color: white;
    margin-bottom: 4rem;
}

.section-badge-modern.light {
    background: rgba(255, 255, 255, 0.15);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
}

.section-title-modern.light {
    color: white;
}

.section-description-modern.light {
    color: rgba(255, 255, 255, 0.9);
}

.text-gradient-light {
    background: linear-gradient(135deg, #60a5fa 0%, #34d399 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.impact-cards-row {
    margin-bottom: 4rem;
}

.impact-card-modern {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 2.5rem 2rem;
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
    height: 100%;
}

.impact-card-modern:hover {
    background: white;
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.impact-card-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
    margin: 0 auto 1.5rem;
    transition: all 0.3s ease;
}

.impact-card-modern:hover .impact-card-icon {
    transform: scale(1.1);
}

.impact-card-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 1rem;
}

.impact-card-description {
    color: #64748b;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.impact-metrics {
    padding-top: 1rem;
    border-top: 1px solid #e2e8f0;
}

.metric {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.25rem;
}

.metric-number {
    font-size: 2rem;
    font-weight: 700;
    color: #22c55e;
}

.metric-label {
    font-size: 0.875rem;
    color: #64748b;
    font-weight: 600;
}

.impact-cta {
    text-align: center;
    color: white;
}

.cta-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: white;
    margin-bottom: 1rem;
}

.cta-description {
    font-size: 1.125rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 2rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.cta-actions {
    display: flex;
    gap: 1.5rem;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
}

.btn-modern-primary.light {
    background: rgba(255, 255, 255, 0.9);
    color: #1e293b;
    box-shadow: 0 4px 15px rgba(255, 255, 255, 0.3);
}

.btn-modern-primary.light:hover {
    background: white;
    color: #1e293b;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 255, 255, 0.4);
}

.btn-modern-outline.light {
    border-color: rgba(255, 255, 255, 0.5);
    color: white;
}

.btn-modern-outline.light:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: white;
}

/* Services Page Styles */
.services-hero-section {
    position: relative;
    min-height: 70vh;
    display: flex;
    align-items: center;
    overflow: hidden;
    background: linear-gradient(135deg, #1e3a8a 0%, #059669 100%);
}

.hero-content-services {
    text-align: center;
    color: white;
    padding: 2rem 0;
}

.hero-badge-services {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 50px;
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: 2rem;
}

.hero-title-services {
    font-size: 3rem;
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: 1.5rem;
    color: white;
}

.text-gradient-services {
    background: linear-gradient(135deg, #60a5fa 0%, #34d399 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-description-services {
    font-size: 1.25rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 3rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.6;
}

.hero-actions-services {
    display: flex;
    gap: 1.5rem;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    margin-bottom: 3rem;
}

.btn-primary-services {
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
    color: white;
    padding: 1rem 2rem;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 4px 15px rgba(34, 197, 94, 0.3);
}

.btn-primary-services:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(34, 197, 94, 0.4);
    color: white;
    text-decoration: none;
}

.hero-scroll-indicator-services {
    color: rgba(255, 255, 255, 0.7);
    animation: bounce 2s infinite;
}

.scroll-arrow-services {
    font-size: 1.5rem;
}

/* Service Introduction */
.service-intro-section {
    padding: 6rem 0;
    background: #f8fafc;
}

.intro-content-services {
    padding: 2rem 0;
}

.section-badge-services {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: #e0f2fe;
    color: #0369a1;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
}

.section-title-services {
    font-size: 2.5rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.text-accent-services {
    color: #059669;
}

.intro-description-services {
    font-size: 1.125rem;
    color: #64748b;
    line-height: 1.7;
    margin-bottom: 3rem;
}

.intro-highlights-services {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.highlight-item-services {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.highlight-icon-services {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    flex-shrink: 0;
}

.highlight-text-services h4 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 0.5rem;
}

.highlight-text-services p {
    color: #64748b;
    margin: 0;
    line-height: 1.6;
}

.intro-visual-services {
    position: relative;
}

.visual-card-services {
    position: relative;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.intro-image-services {
    width: 100%;
    height: 400px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.visual-card-services:hover .intro-image-services {
    transform: scale(1.05);
}

.visual-overlay-services {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.4);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.visual-card-services:hover .visual-overlay-services {
    opacity: 1;
}

.play-button-services {
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #059669;
    font-size: 2rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.play-button-services:hover {
    background: white;
    transform: scale(1.1);
}

/* Service Grid */
.service-grid-section {
    padding: 6rem 0;
    background: white;
}

.section-header-services {
    margin-bottom: 4rem;
}

.section-description-services {
    font-size: 1.125rem;
    color: #64748b;
    max-width: 700px;
    margin: 0 auto 2rem;
    line-height: 1.7;
}

.service-card-services {
    background: white;
    border-radius: 20px;
    padding: 2.5rem 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    height: 100%;
    border: 1px solid #f1f5f9;
    text-decoration: none;
    color: inherit;
    display: block;
}

.service-card-services:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    border-color: #22c55e;
    text-decoration: none;
    color: inherit;
}

.service-icon-services {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
    margin-bottom: 1.5rem;
    transition: all 0.3s ease;
}

.service-card-services:hover .service-icon-services {
    transform: scale(1.1);
}

.service-title-services {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 1rem;
}

.service-description-services {
    color: #64748b;
    line-height: 1.6;
    margin-bottom: 0;
}

/* Call to Action Section */
.cta-section {
    position: relative;
    padding: 6rem 0;
    background: linear-gradient(135deg, #1e3a8a 0%, #059669 100%);
    overflow: hidden;
}

.cta-content-services {
    text-align: center;
    color: white;
}

.section-badge-services.light {
    background: rgba(255, 255, 255, 0.15);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
}

.section-title-services.light {
    color: white;
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.section-description-services.light {
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 2rem;
}

.cta-actions-services {
    display: flex;
    gap: 1.5rem;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
}

.btn-primary-services.light {
    background: rgba(255, 255, 255, 0.9);
    color: #1e293b;
    box-shadow: 0 4px 15px rgba(255, 255, 255, 0.3);
}

.btn-primary-services.light:hover {
    background: white;
    color: #1e293b;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 255, 255, 0.4);
}

/* Modern About Page Responsive Styles */
@media (max-width: 768px) {
    .hero-title-modern {
        font-size: 2.5rem;
    }

    .hero-description-modern {
        font-size: 1.125rem;
    }

    .hero-actions-modern {
        flex-direction: column;
        gap: 1rem;
    }

    .section-title-modern {
        font-size: 2rem;
    }

    .intro-highlights {
        gap: 1.5rem;
    }

    .highlight-item {
        flex-direction: column;
        text-align: center;
    }

    .highlight-icon {
        margin-bottom: 1rem;
    }

    .floating-stats {
        position: static;
        justify-content: center;
        margin-top: 2rem;
    }

    .timeline-modern {
        padding-left: 1rem;
    }

    .timeline-modern::before {
        left: 15px;
    }

    .timeline-item-modern {
        flex-direction: column;
        gap: 1rem;
    }

    .timeline-item-modern::before {
        left: -32px;
    }

    .timeline-year-modern {
        align-self: flex-start;
        margin-left: 2rem;
    }

    .timeline-content-modern:hover {
        transform: none;
    }

    .cta-title {
        font-size: 2rem;
    }

    .cta-actions {
        flex-direction: column;
        gap: 1rem;
    }

    /* Services Page Mobile */
    .hero-title-services {
        font-size: 2rem;
    }

    .hero-description-services {
        font-size: 1.125rem;
    }

    .section-title-services {
        font-size: 1.8rem;
    }

    .intro-highlights-services {
        gap: 1.5rem;
    }

    .highlight-item-services {
        flex-direction: column;
        text-align: center;
    }

    .highlight-icon-services {
        margin-bottom: 1rem;
    }
}

@media (max-width: 576px) {
    .modern-hero-section {
        min-height: 60vh;
        padding: 2rem 0;
    }

    .hero-title-modern {
        font-size: 2rem;
    }

    .hero-description-modern {
        font-size: 1rem;
    }

    .section-title-modern {
        font-size: 1.75rem;
    }

    .service-card,
    .stat-card-modern,
    .impact-card-modern {
        padding: 2rem 1.5rem;
    }

    .stat-number-modern {
        font-size: 2.5rem;
    }

    .stat-unit {
        font-size: 1.5rem;
    }

    .timeline-year-modern {
        font-size: 1rem;
        padding: 0.5rem 1rem;
        margin-left: 1rem;
    }

    .timeline-content-modern {
        padding: 1.5rem;
    }

    .cta-title {
        font-size: 1.75rem;
    }

    /* Services Page Small Mobile */
    .services-hero-section {
        min-height: 60vh;
    }

    .hero-title-services {
        font-size: 1.75rem;
    }

    .hero-description-services {
        font-size: 1rem;
    }

    .section-title-services {
        font-size: 1.5rem;
    }

    .service-card-services {
        padding: 2rem 1.5rem;
    }
}

/* Services Mega Menu Styles */
.services-mega-menu {
    min-width: 400px;
    padding: 0;
    border: none;
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    background: white;
    margin-top: 0.5rem;
}

.mega-menu-header {
    padding: 1.5rem 2rem 1rem;
    border-bottom: 1px solid #f1f5f9;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 15px 15px 0 0;
}

.mega-menu-header h6 {
    font-size: 1.1rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 0.5rem;
}

.mega-menu-header p {
    font-size: 0.875rem;
    color: #64748b;
    margin: 0;
}

.mega-menu-section {
    padding: 1rem 0;
}

.mega-menu-section .section-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: #64748b;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin: 0 2rem 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #f1f5f9;
}

.mega-menu-links {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.mega-menu-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 2rem;
    color: #374151;
    text-decoration: none;
    transition: all 0.3s ease;
    border: none;
    background: none;
}

.mega-menu-item:hover {
    background: linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 100%);
    color: #22c55e;
    text-decoration: none;
}

.mega-menu-item i {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.1rem;
    flex-shrink: 0;
    transition: all 0.3s ease;
}

.mega-menu-item:hover i {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(34, 197, 94, 0.3);
}

.mega-menu-item div {
    flex: 1;
}

.mega-menu-item strong {
    display: block;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 0.25rem;
}

.mega-menu-item span {
    font-size: 0.875rem;
    color: #64748b;
    line-height: 1.4;
}

.mega-menu-footer {
    padding: 1rem 2rem 1.5rem;
    border-top: 1px solid #f1f5f9;
    background: #f8fafc;
    border-radius: 0 0 15px 15px;
}

.mega-menu-footer .btn {
    border-radius: 10px;
    font-weight: 600;
    padding: 0.75rem 1.5rem;
}

/* Ensure dropdown shows on hover for desktop */
@media (min-width: 992px) {
    .nav-item.dropdown:hover .dropdown-menu {
        display: block;
        margin-top: 0;
    }
}

/* Off-canvas Services Menu Styles */
.offcanvas-trigger {
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
}

.offcanvas-trigger:hover {
    color: #22c55e !important;
}

.offcanvas-trigger i {
    font-size: 0.75rem;
    transition: transform 0.3s ease;
}

.services-offcanvas {
    width: 400px;
    background: white;
    border-left: 3px solid #22c55e;
}

.services-offcanvas .offcanvas-header {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-bottom: 1px solid #e2e8f0;
    padding: 1.5rem 2rem;
}

.services-offcanvas .offcanvas-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: #1e293b;
    margin: 0;
}

.services-offcanvas .btn-close {
    background-size: 1rem;
    opacity: 0.7;
}

.services-offcanvas .offcanvas-body {
    padding: 0;
}

.services-menu-header {
    padding: 2rem 2rem 1rem;
    border-bottom: 1px solid #f1f5f9;
}

.services-menu-header h6 {
    font-size: 1.1rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 0.5rem;
}

.services-menu-header p {
    font-size: 0.9rem;
    color: #64748b;
    margin: 0;
}

.services-menu-section {
    padding: 1.5rem 0;
    border-bottom: 1px solid #f1f5f9;
}

.services-menu-section:last-of-type {
    border-bottom: none;
}

.services-menu-section .section-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: #64748b;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 1rem;
    padding: 0 2rem;
}

.services-menu-links {
    display: flex;
    flex-direction: column;
}

.service-menu-item {
    display: flex;
    align-items: center;
    padding: 1rem 2rem;
    text-decoration: none;
    color: inherit;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.service-menu-item:hover {
    background: #f8fffe;
    border-left-color: #22c55e;
    text-decoration: none;
    color: inherit;
}

.service-menu-icon {
    width: 45px;
    height: 45px;
    background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.1rem;
    margin-right: 1rem;
    flex-shrink: 0;
    box-shadow: 0 4px 12px rgba(34, 197, 94, 0.3);
}

.service-menu-content {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.service-menu-content strong {
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 0.25rem;
    font-size: 0.95rem;
}

.service-menu-content span {
    font-size: 0.8rem;
    color: #64748b;
    line-height: 1.4;
}

.service-menu-arrow {
    color: #cbd5e1;
    font-size: 0.8rem;
    transition: all 0.3s ease;
}

.service-menu-item:hover .service-menu-arrow {
    color: #22c55e;
    transform: translateX(3px);
}

.services-menu-footer {
    padding: 2rem;
    background: #f8fafc;
    border-top: 1px solid #e2e8f0;
}

.services-menu-footer .btn {
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    border-radius: 10px;
}

/* Off-canvas Mobile Styles */
@media (max-width: 768px) {
    .services-offcanvas {
        width: 100%;
        max-width: 350px;
    }

    .services-menu-header {
        padding: 1.5rem 1.5rem 1rem;
    }

    .services-menu-section .section-title {
        padding: 0 1.5rem;
    }

    .service-menu-item {
        padding: 0.875rem 1.5rem;
    }

    .service-menu-icon {
        width: 40px;
        height: 40px;
        font-size: 1rem;
        margin-right: 0.875rem;
    }

    .service-menu-content strong {
        font-size: 0.9rem;
    }

    .service-menu-content span {
        font-size: 0.75rem;
    }

    .services-menu-footer {
        padding: 1.5rem;
    }
}
