# Image Management System Guide

## Directory Structure Created

```
assets/images/
├── hero/          # Hero section background images
├── about/         # About page images
├── services/      # Service-related images
├── projects/      # Project showcase images
├── team/          # Team member photos (if needed)
└── logos/         # Company logos and brand assets
```

## Recommended Image Specifications

### Hero Section Images
- **Size**: 1920x1080px (Full HD)
- **Format**: JPG (for photos), WebP (for better compression)
- **Quality**: 85-90% compression
- **Usage**: Background images for hero sections

### Project Images
- **Size**: 800x600px
- **Format**: JPG or WebP
- **Quality**: 80-85% compression
- **Usage**: Project showcase cards

### Service Images
- **Size**: 600x400px
- **Format**: JPG or WebP
- **Quality**: 80% compression
- **Usage**: Service detail pages and cards

### About Page Images
- **Size**: 800x600px
- **Format**: JPG or WebP
- **Quality**: 85% compression
- **Usage**: Company introduction sections

### Logo Images
- **Size**: SVG (scalable) or PNG (300x300px minimum)
- **Format**: SVG preferred, PNG with transparency
- **Usage**: Header, footer, and branding

## Current Unsplash Images to Replace

### Hero Sections
1. **Index Hero**: `https://images.unsplash.com/photo-1466611653911-95081537e5b7`
   - Replace with: `assets/images/hero/main-hero.jpg`
   - Suggested: Wind turbines or renewable energy landscape

2. **About Hero**: `https://images.unsplash.com/photo-1559302504-64aae6ca6b6d`
   - Replace with: `assets/images/hero/about-hero.jpg`
   - Suggested: Team collaboration or office environment

3. **Team Hero**: `https://images.unsplash.com/photo-1522071820081-009f0129c71c`
   - Replace with: `assets/images/hero/team-hero.jpg`
   - Suggested: Professional team meeting or office space

### Project Images
1. **North Sea Wind Farm**: `https://images.unsplash.com/photo-1466611653911-95081537e5b7`
   - Replace with: `assets/images/projects/north-sea-wind.jpg`

2. **Atlantic Coast Development**: `https://images.unsplash.com/photo-1497435334941-8c899ee9e8e9`
   - Replace with: `assets/images/projects/atlantic-coast.jpg`

3. **Pacific Solar Array**: `https://images.unsplash.com/photo-1509391366360-2e959784a276`
   - Replace with: `assets/images/projects/pacific-solar.jpg`

4. **Nordic Energy Storage**: `https://images.unsplash.com/photo-1558618666-fcd25c85cd64`
   - Replace with: `assets/images/projects/nordic-storage.jpg`

5. **Australian Hybrid Complex**: `https://images.unsplash.com/photo-1497435334941-8c899ee9e8e9`
   - Replace with: `assets/images/projects/australian-hybrid.jpg`

6. **Mediterranean Wind Initiative**: `https://images.unsplash.com/photo-1466611653911-95081537e5b7`
   - Replace with: `assets/images/projects/mediterranean-wind.jpg`

### About Page Images
1. **Company Introduction**: `https://images.unsplash.com/photo-1559302504-64aae6ca6b6d`
   - Replace with: `assets/images/about/company-intro.jpg`

## Implementation Steps

### Step 1: Download and Optimize Images
1. Source high-quality images for each category
2. Resize images to recommended specifications
3. Optimize for web (compress without losing quality)
4. Save in appropriate directories

### Step 2: Update Image References
Replace Unsplash URLs with local paths in these files:
- `index.php`
- `about.php`
- `projects.php`
- `team.php`
- `service-detail.php`

### Step 3: Add Fallback Images
Create placeholder images for missing content:
```html
<img src="assets/images/hero/default-hero.jpg" 
     alt="Renewable Energy" 
     onerror="this.src='https://via.placeholder.com/1920x1080/22c55e/ffffff?text=Eldergrove+Energy'">
```

## Image Optimization Tools

### Recommended Tools:
1. **TinyPNG** - Online compression
2. **ImageOptim** - Mac optimization tool
3. **Squoosh** - Google's web-based optimizer
4. **GIMP** - Free image editor

### Batch Processing:
For multiple images, use tools like:
- **ImageMagick** (command line)
- **Adobe Photoshop** (batch actions)
- **XnConvert** (free batch converter)

## SEO Best Practices

### Alt Text Guidelines:
- Descriptive and specific
- Include relevant keywords naturally
- Keep under 125 characters
- Don't start with "Image of" or "Picture of"

### File Naming:
- Use descriptive names: `wind-turbine-offshore.jpg`
- Include keywords: `renewable-energy-solar-panel.jpg`
- Use hyphens, not underscores
- Keep names concise but descriptive

## Performance Considerations

### Lazy Loading:
```html
<img src="assets/images/projects/project1.jpg" 
     alt="Wind Farm Project" 
     loading="lazy">
```

### Responsive Images:
```html
<picture>
  <source media="(max-width: 768px)" srcset="assets/images/hero/mobile-hero.jpg">
  <source media="(max-width: 1200px)" srcset="assets/images/hero/tablet-hero.jpg">
  <img src="assets/images/hero/desktop-hero.jpg" alt="Hero Image">
</picture>
```

### WebP Format Support:
```html
<picture>
  <source srcset="assets/images/hero/hero.webp" type="image/webp">
  <img src="assets/images/hero/hero.jpg" alt="Hero Image">
</picture>
```

## Next Steps

1. **Immediate**: Replace hero section images first (highest impact)
2. **Priority**: Update project images for visual consistency
3. **Secondary**: Replace about page and service images
4. **Optional**: Add team photos if desired in the future

## Maintenance

- Review image performance monthly
- Update images when content changes
- Monitor loading times and optimize as needed
- Keep backup copies of original high-resolution images
