<?php
/**
 * Contact Form Handler for Eldergrove Energy
 * Simple PHP script to handle contact form submissions
 */

// Enable error reporting for development (remove in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Set content type
header('Content-Type: application/json');

// Allow CORS if needed
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Response array
$response = array();

// Check if form was submitted via POST
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    
    // Sanitize and validate input data
    $name = isset($_POST['name']) ? trim(htmlspecialchars($_POST['name'])) : '';
    $email = isset($_POST['email']) ? trim(htmlspecialchars($_POST['email'])) : '';
    $company = isset($_POST['company']) ? trim(htmlspecialchars($_POST['company'])) : '';
    $subject = isset($_POST['subject']) ? trim(htmlspecialchars($_POST['subject'])) : '';
    $message = isset($_POST['message']) ? trim(htmlspecialchars($_POST['message'])) : '';
    
    // Validation
    $errors = array();
    
    if (empty($name)) {
        $errors[] = 'Name is required';
    }
    
    if (empty($email)) {
        $errors[] = 'Email is required';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'Invalid email format';
    }
    
    if (empty($subject)) {
        $errors[] = 'Subject is required';
    }
    
    if (empty($message)) {
        $errors[] = 'Message is required';
    }
    
    // Check for spam (simple honeypot and time-based protection)
    $honeypot = isset($_POST['website']) ? $_POST['website'] : '';
    if (!empty($honeypot)) {
        $errors[] = 'Spam detected';
    }
    
    // If no errors, process the form
    if (empty($errors)) {
        
        // Email configuration
        $to = '<EMAIL>'; // Change this to your actual email
        $email_subject = 'New Contact Form Submission: ' . $subject;
        
        // Email body
        $email_body = "
        New contact form submission from Eldergrove Energy website:
        
        Name: $name
        Email: $email
        Company: $company
        Subject: $subject
        
        Message:
        $message
        
        ---
        Submitted on: " . date('Y-m-d H:i:s') . "
        IP Address: " . $_SERVER['REMOTE_ADDR'] . "
        User Agent: " . $_SERVER['HTTP_USER_AGENT'] . "
        ";
        
        // Email headers
        $headers = array(
            'From: ' . $email,
            'Reply-To: ' . $email,
            'X-Mailer: PHP/' . phpversion(),
            'Content-Type: text/plain; charset=UTF-8'
        );
        
        // Send email
        if (mail($to, $email_subject, $email_body, implode("\r\n", $headers))) {
            
            // Log successful submission (optional)
            $log_entry = date('Y-m-d H:i:s') . " - Contact form submission from: $name ($email)\n";
            file_put_contents('contact_log.txt', $log_entry, FILE_APPEND | LOCK_EX);
            
            $response['success'] = true;
            $response['message'] = 'Thank you for your message! We\'ll get back to you soon.';
            
        } else {
            $response['success'] = false;
            $response['message'] = 'Sorry, there was an error sending your message. Please try again later.';
        }
        
    } else {
        $response['success'] = false;
        $response['message'] = 'Please correct the following errors:';
        $response['errors'] = $errors;
    }
    
} else {
    $response['success'] = false;
    $response['message'] = 'Invalid request method';
}

// Return JSON response
echo json_encode($response);

// Alternative: If you want to redirect back to the contact page with a message
// You can uncomment the following and comment out the JSON response above

/*
if (isset($response['success']) && $response['success']) {
    header('Location: index.html#contact?success=1');
} else {
    header('Location: index.html#contact?error=1');
}
exit;
*/
?>
