<?php include 'header.php'; ?>

    <!-- Hero Section with Background Image Slider -->
    <section id="home" class="hero-slider">
        <!-- Slide 1 - Offshore Wind -->
        <div class="hero-slide active">
            <div class="hero-slide-background">
                <img src="https://images.unsplash.com/photo-1548337138-e87d889cc369?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80" alt="Offshore Wind Farm" class="hero-slide-bg-img">
                <div class="hero-slide-overlay"></div>
            </div>
            <div class="container">
                <div class="row justify-content-center text-center">
                    <div class="col-lg-10">
                        <div class="hero-content">
                            <h1 class="hero-main-title">
                                Powering the Future with<br>
                                <span class="text-gradient animated-text">
                                    <span class="text-item active">Offshore Wind</span>
                                    <span class="text-item">Solar Innovation</span>
                                    <span class="text-item">Renewable Energy</span>
                                    <span class="text-item">Clean Technology</span>
                                </span>
                            </h1>
                            <p class="hero-description">
                                <PERSON><PERSON> is the global leader in offshore wind and one of the largest renewable
                                energy companies in the world, driving sustainable innovation across the seas.
                            </p>
                            <div class="hero-actions">
                                <a href="#projects" class="btn-primary">Explore Our Projects</a>
                                <a href="#about" class="btn-outline-white">Learn More</a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Statistics Cards -->
                <div class="row justify-content-center">
                    <div class="col-lg-10">
                        <div class="hero-stats-cards">
                            <div class="stat-card">
                                <div class="stat-number">25+</div>
                                <div class="stat-label">Countries Served</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">15GW</div>
                                <div class="stat-label">Installed Capacity</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">50M</div>
                                <div class="stat-label">Homes Powered</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">98%</div>
                                <div class="stat-label">Efficiency Rate</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 2 - Solar Innovation -->
        <div class="hero-slide">
            <div class="hero-slide-background">
                <img src="https://images.unsplash.com/photo-1509391366360-2e959784a276?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80" alt="Solar Energy Farm" class="hero-slide-bg-img">
                <div class="hero-slide-overlay"></div>
            </div>
            <div class="container">
                <div class="row justify-content-center text-center">
                    <div class="col-lg-10">
                        <div class="hero-content">
                            <h1 class="hero-main-title">
                                Revolutionizing Energy with<br>
                                <span class="text-gradient animated-text-2">
                                    <span class="text-item-2 active">Solar Innovation</span>
                                    <span class="text-item-2">Smart Storage</span>
                                    <span class="text-item-2">Grid Solutions</span>
                                    <span class="text-item-2">Clean Power</span>
                                </span>
                            </h1>
                            <p class="hero-description">
                                Advanced solar technology combined with cutting-edge energy storage systems,
                                delivering reliable clean electricity around the clock for a sustainable future.
                            </p>
                            <div class="hero-actions">
                                <a href="#technology" class="btn-primary">Our Technology</a>
                                <a href="#sustainability" class="btn-outline-white">Learn More</a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Statistics Cards -->
                <div class="row justify-content-center">
                    <div class="col-lg-10">
                        <div class="hero-stats-cards">
                            <div class="stat-card">
                                <div class="stat-number">12GW</div>
                                <div class="stat-label">Solar Capacity</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">8GWh</div>
                                <div class="stat-label">Storage Systems</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">99.8%</div>
                                <div class="stat-label">System Reliability</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">35+</div>
                                <div class="stat-label">Solar Farms</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 3 - Renewable Energy -->
        <div class="hero-slide">
            <div class="hero-slide-background">
                <img src="https://images.unsplash.com/photo-1473341304170-971dccb5ac1e?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80" alt="Renewable Energy Infrastructure" class="hero-slide-bg-img">
                <div class="hero-slide-overlay"></div>
            </div>
            <div class="container">
                <div class="row justify-content-center text-center">
                    <div class="col-lg-10">
                        <div class="hero-content">
                            <h1 class="hero-main-title">
                                Building Tomorrow's<br>
                                <span class="text-gradient animated-text-3">
                                    <span class="text-item-3 active">Renewable Future</span>
                                    <span class="text-item-3">Green Economy</span>
                                    <span class="text-item-3">Sustainable World</span>
                                    <span class="text-item-3">Clean Planet</span>
                                </span>
                            </h1>
                            <p class="hero-description">
                                Creating a comprehensive renewable energy ecosystem with biomass, hydroelectric,
                                and smart grid technologies for a completely sustainable energy future.
                            </p>
                            <div class="hero-actions">
                                <a href="#sustainability" class="btn-primary">Our Impact</a>
                                <a href="#contact" class="btn-outline-white">Get Started</a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Statistics Cards -->
                <div class="row justify-content-center">
                    <div class="col-lg-10">
                        <div class="hero-stats-cards">
                            <div class="stat-card">
                                <div class="stat-number">40GW</div>
                                <div class="stat-label">Total Portfolio</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">85%</div>
                                <div class="stat-label">Carbon Reduction</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">2030</div>
                                <div class="stat-label">Net Zero Target</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">120+</div>
                                <div class="stat-label">Green Projects</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slider Controls -->
        <div class="hero-slider-controls">
            <div class="slider-dot active" data-slide="0"></div>
            <div class="slider-dot" data-slide="1"></div>
            <div class="slider-dot" data-slide="2"></div>
        </div>

        <!-- Navigation Arrows -->
        <button class="slider-nav slider-prev" aria-label="Previous slide">
            <i class="fas fa-chevron-left"></i>
        </button>
        <button class="slider-nav slider-next" aria-label="Next slide">
            <i class="fas fa-chevron-right"></i>
        </button>
    </section>

    <!-- Rest of content would go here -->
    <section class="bg-light py-5">
        <div class="container">
            <div class="text-center">
                <h2>This is the animated version with different text animations on each slide</h2>
                <p>Each slide has its own animated text that cycles through 4 different phrases.</p>
            </div>
        </div>
    </section>

    <?php include 'footer.php'; ?>

    <!-- Custom Slider JS with Multiple Animations -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Hero Slider Functionality (same as before)
            const heroSlider = document.querySelector('.hero-slider');
            if (heroSlider) {
                const slides = heroSlider.querySelectorAll('.hero-slide');
                const dots = heroSlider.querySelectorAll('.slider-dot');
                const prevBtn = heroSlider.querySelector('.slider-prev');
                const nextBtn = heroSlider.querySelector('.slider-next');
                
                let currentSlide = 0;
                const totalSlides = slides.length;
                let slideInterval = setInterval(nextSlide, 7000);
                
                function showSlide(n) {
                    slides.forEach(slide => slide.classList.remove('active'));
                    dots.forEach(dot => dot.classList.remove('active'));
                    
                    if (n >= totalSlides) currentSlide = 0;
                    if (n < 0) currentSlide = totalSlides - 1;
                    
                    slides[currentSlide].classList.add('active');
                    dots[currentSlide].classList.add('active');
                }
                
                function nextSlide() {
                    currentSlide++;
                    showSlide(currentSlide);
                }
                
                function prevSlide() {
                    currentSlide--;
                    showSlide(currentSlide);
                }
                
                nextBtn.addEventListener('click', () => {
                    clearInterval(slideInterval);
                    nextSlide();
                    slideInterval = setInterval(nextSlide, 7000);
                });
                
                prevBtn.addEventListener('click', () => {
                    clearInterval(slideInterval);
                    prevSlide();
                    slideInterval = setInterval(nextSlide, 7000);
                });
                
                dots.forEach((dot, index) => {
                    dot.addEventListener('click', () => {
                        clearInterval(slideInterval);
                        currentSlide = index;
                        showSlide(currentSlide);
                        slideInterval = setInterval(nextSlide, 7000);
                    });
                });
                
                heroSlider.addEventListener('mouseenter', () => clearInterval(slideInterval));
                heroSlider.addEventListener('mouseleave', () => slideInterval = setInterval(nextSlide, 7000));
            }

            // Animated Text for each slide
            function initAnimatedText(selector, delay = 0) {
                const animatedText = document.querySelector(selector);
                if (animatedText) {
                    const textItems = animatedText.querySelectorAll('[class*="text-item"]');
                    let currentTextIndex = 0;
                    
                    function showNextText() {
                        textItems.forEach(item => item.classList.remove('active'));
                        currentTextIndex = (currentTextIndex + 1) % textItems.length;
                        textItems[currentTextIndex].classList.add('active');
                    }
                    
                    if (textItems.length > 0) {
                        textItems[0].classList.add('active');
                    }
                    
                    setTimeout(() => {
                        setInterval(showNextText, 2500);
                    }, delay);
                }
            }

            // Initialize animated text for each slide with different delays
            initAnimatedText('.animated-text', 0);
            initAnimatedText('.animated-text-2', 500);
            initAnimatedText('.animated-text-3', 1000);

            console.log('Animated slider loaded successfully!');
        });
    </script>
